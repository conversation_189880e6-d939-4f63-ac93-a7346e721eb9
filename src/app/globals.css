@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Fix for text input visibility - ensure all text inputs have proper contrast */
/* Use higher specificity to override Tailwind classes */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
input[type="url"],
input[type="tel"],
input[type="number"],
input[type="date"],
textarea,
select {
  color: #111827 !important; /* Very dark gray text for maximum contrast */
  -webkit-text-fill-color: #111827 !important; /* For Safari/WebKit */
}

/* Target all inputs more broadly */
input,
textarea,
select {
  color: #111827 !important;
  -webkit-text-fill-color: #111827 !important;
}

/* Force text color on focus and when typing */
input:focus,
textarea:focus,
input:active,
textarea:active {
  color: #111827 !important;
  -webkit-text-fill-color: #111827 !important;
}

/* Ensure placeholder text is visible but lighter */
input::placeholder,
textarea::placeholder {
  color: #6b7280 !important; /* Medium gray for placeholders */
  opacity: 1 !important;
  -webkit-text-fill-color: #6b7280 !important;
}

/* Override any Tailwind utility classes that might be interfering */
.text-gray-500,
.text-gray-400,
.text-gray-300 {
  color: #111827 !important;
}

/* Specific overrides for form inputs */
form input,
form textarea,
form select {
  color: #111827 !important;
  -webkit-text-fill-color: #111827 !important;
}

/* Additional specificity for common input containers */
div input,
div textarea,
.space-y-4 input,
.space-y-6 input,
.grid input,
.relative input {
  color: #111827 !important;
  -webkit-text-fill-color: #111827 !important;
}

/* Force override any CSS variables or computed styles */
* input,
* textarea {
  color: #111827 !important;
  -webkit-text-fill-color: #111827 !important;
}

/* Ensure text remains visible during typing */
input[value],
textarea[value] {
  color: #111827 !important;
  -webkit-text-fill-color: #111827 !important;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="search"],
  input[type="url"],
  input[type="tel"],
  input[type="number"],
  input[type="date"],
  textarea,
  select,
  input,
  form input,
  form textarea,
  form select,
  div input,
  div textarea,
  .space-y-4 input,
  .space-y-6 input,
  .grid input,
  .relative input,
  * input,
  * textarea,
  input[value],
  textarea[value] {
    color: #f9fafb !important; /* Light text for dark mode */
    -webkit-text-fill-color: #f9fafb !important;
  }

  input:focus,
  textarea:focus,
  input:active,
  textarea:active {
    color: #f9fafb !important;
    -webkit-text-fill-color: #f9fafb !important;
  }

  input::placeholder,
  textarea::placeholder {
    color: #9ca3af !important; /* Lighter gray for dark mode placeholders */
    opacity: 1 !important;
    -webkit-text-fill-color: #9ca3af !important;
  }
}
