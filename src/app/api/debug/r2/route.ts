import { NextRequest, NextResponse } from 'next/server';
import CloudflareR2Service from '@/lib/cloudflare-images';

export async function GET(request: NextRequest) {
  try {
    const r2Service = new CloudflareR2Service();
    
    // Check configuration
    const config = {
      hasAccountId: !!process.env.CLOUDFLARE_ACCOUNT_ID,
      hasAccessKey: !!process.env.R2_ACCESS_KEY_ID,
      hasSecretKey: !!process.env.R2_SECRET_ACCESS_KEY,
      hasBucketName: !!process.env.R2_BUCKET_NAME,
      hasPublicUrl: !!process.env.R2_PUBLIC_URL,
      accountId: process.env.CLOUDFLARE_ACCOUNT_ID ? 
        process.env.CLOUDFLARE_ACCOUNT_ID.substring(0, 8) + '...' : 'Not set',
      bucketName: process.env.R2_BUCKET_NAME || 'Not set',
      publicUrl: process.env.R2_PUBLIC_URL || 'Not set',
    };
    
    // Test URL generation
    const testKey = 'profile-images/test-image.jpg';
    const testUrl = r2Service.getImageUrl(testKey);
    
    return NextResponse.json({
      success: true,
      config,
      testUrl,
      message: 'R2 configuration check complete'
    });
  } catch (error) {
    console.error('R2 debug error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      config: {
        hasAccountId: !!process.env.CLOUDFLARE_ACCOUNT_ID,
        hasAccessKey: !!process.env.R2_ACCESS_KEY_ID,
        hasSecretKey: !!process.env.R2_SECRET_ACCESS_KEY,
        hasBucketName: !!process.env.R2_BUCKET_NAME,
        hasPublicUrl: !!process.env.R2_PUBLIC_URL,
      }
    }, { status: 500 });
  }
}
