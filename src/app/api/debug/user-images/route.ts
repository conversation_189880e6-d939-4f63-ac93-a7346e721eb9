import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Employee from '@/models/Employee';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    await dbConnect();
    
    const employee = await Employee.findOne({ email: session.user.email });
    
    return NextResponse.json({
      success: true,
      data: {
        sessionUserImage: session.user.image,
        employeeImage: employee?.image,
        employeeName: employee?.name,
        employeeEmail: employee?.email,
      }
    });
  } catch (error) {
    console.error('User images debug error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
