import { NextRequest, NextResponse } from 'next/server';
import CloudflareR2Service from '@/lib/cloudflare-images';

export async function POST(request: NextRequest) {
  try {
    const r2Service = new CloudflareR2Service();
    
    // Configure CORS for the bucket
    const corsConfigured = await r2Service.configureBucketCors();
    
    return NextResponse.json({
      success: true,
      corsConfigured,
      message: corsConfigured 
        ? 'R2 bucket CORS configured successfully' 
        : 'Failed to configure CORS - check logs for details'
    });
  } catch (error) {
    console.error('R2 setup error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
