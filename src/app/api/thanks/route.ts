import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Thanks from '@/models/Thanks';
import Employee from '@/models/Employee';
import NotificationService from '@/lib/notifications';
import { getOrganizationFromRequest } from '@/lib/middleware/organization';

export async function GET(request: NextRequest) {
  try {
    // Allow both authenticated and unauthenticated users to view public thanks
    const session = await getServerSession(authOptions);

    await dbConnect();

    // Get organization context - for anonymous users, try to get from URL params
    let orgContext = null;
    if (session) {
      orgContext = await getOrganizationFromRequest(request);
    } else {
      // For anonymous users, we'll show all public thanks for now
      // In a real implementation, you might want to determine organization from URL or other means
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const toEmployee = searchParams.get('toEmployee');
    const fromEmployee = searchParams.get('fromEmployee');
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');

    let query: any = {
      isPublic: true
    };

    // Add organization filter if we have org context
    if (orgContext?.organization?._id) {
      query.organization = orgContext.organization._id;
    }

    if (category) {
      query.category = category;
    }

    if (toEmployee) {
      query.toEmployee = toEmployee;
    }

    if (fromEmployee) {
      query.fromEmployee = fromEmployee;
    }

    const thanks = await Thanks.find(query)
      .populate('fromEmployee', 'name email image department position')
      .populate('toEmployee', 'name email image department position')
      .select('-__v')
      .limit(limit)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Thanks.countDocuments(query);

    return NextResponse.json({
      thanks,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching thanks:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    await dbConnect();

    const body = await request.json();
    const { toEmployee, message, category, isPublic = true, aiGenerated = false, anonymousSender } = body;

    // Validate required fields
    if (!toEmployee || !message || !category) {
      return NextResponse.json(
        { error: 'To employee, message, and category are required' },
        { status: 400 }
      );
    }

    // For anonymous users, validate anonymous sender info
    if (!session?.user?.email && (!anonymousSender?.name || !anonymousSender.name.trim())) {
      return NextResponse.json(
        { error: 'Anonymous sender name is required' },
        { status: 400 }
      );
    }

    let fromEmployeeRecord = null;
    let orgContext = null;

    if (session?.user?.email) {
      // Authenticated user - get organization context and employee record
      orgContext = await getOrganizationFromRequest(request);
      if (!orgContext) {
        return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
      }

      fromEmployeeRecord = await Employee.findOne({
        email: session.user.email,
        organization: orgContext.organization._id
      });
      if (!fromEmployeeRecord) {
        return NextResponse.json(
          { error: 'Employee record not found' },
          { status: 404 }
        );
      }
    }

    // Validate that toEmployee exists
    const toEmployeeRecord = await Employee.findById(toEmployee);
    if (!toEmployeeRecord) {
      return NextResponse.json(
        { error: 'Target employee not found' },
        { status: 404 }
      );
    }

    // Prevent self-thanks for authenticated users
    if (fromEmployeeRecord && fromEmployeeRecord._id.toString() === toEmployee) {
      return NextResponse.json(
        { error: 'Cannot send thanks to yourself' },
        { status: 400 }
      );
    }

    const thanksData: any = {
      organization: orgContext?.organization?._id || toEmployeeRecord.organization,
      toEmployee,
      message: message.trim(),
      category,
      isPublic,
      aiGenerated,
      reactions: []
    };

    // Add fromEmployee for authenticated users or anonymous sender info
    if (fromEmployeeRecord) {
      thanksData.fromEmployee = fromEmployeeRecord._id;
    } else {
      thanksData.anonymousSender = {
        name: anonymousSender.name.trim()
      };
    }

    const thanks = new Thanks(thanksData);
    await thanks.save();

    // Populate the response
    if (fromEmployeeRecord) {
      await thanks.populate('fromEmployee', 'name email image department position');
    }
    await thanks.populate('toEmployee', 'name email image department position');

    // Create notification for the recipient (only for authenticated users)
    if (fromEmployeeRecord && orgContext?.organization?._id) {
      try {
        await NotificationService.createThanksNotification(
          thanks._id,
          toEmployee,
          fromEmployeeRecord._id,
          fromEmployeeRecord.name,
          category,
          orgContext.organization._id
        );
      } catch (notificationError) {
        console.error('Error creating notification:', notificationError);
        // Don't fail the thanks creation if notification fails
      }
    }

    return NextResponse.json(thanks, { status: 201 });

  } catch (error) {
    console.error('Error creating thanks:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
