import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Thanks from '@/models/Thanks';
import Employee from '@/models/Employee';
import NotificationService from '@/lib/notifications';
import { getOrganizationFromRequest } from '@/lib/middleware/organization';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    // Get organization context
    const orgContext = await getOrganizationFromRequest(request);
    if (!orgContext) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const toEmployee = searchParams.get('toEmployee');
    const fromEmployee = searchParams.get('fromEmployee');
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');

    let query: any = {
      organization: orgContext.organization._id,
      isPublic: true
    };

    if (category) {
      query.category = category;
    }

    if (toEmployee) {
      query.toEmployee = toEmployee;
    }

    if (fromEmployee) {
      query.fromEmployee = fromEmployee;
    }

    const thanks = await Thanks.find(query)
      .populate('fromEmployee', 'name email image department position')
      .populate('toEmployee', 'name email image department position')
      .select('-__v')
      .limit(limit)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Thanks.countDocuments(query);

    return NextResponse.json({
      thanks,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching thanks:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    // Get organization context
    const orgContext = await getOrganizationFromRequest(request);
    if (!orgContext) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    const body = await request.json();
    const { toEmployee, message, category, isPublic = true, aiGenerated = false } = body;

    // Validate required fields
    if (!toEmployee || !message || !category) {
      return NextResponse.json(
        { error: 'To employee, message, and category are required' },
        { status: 400 }
      );
    }

    // Get the current user's employee record (within organization)
    const fromEmployeeRecord = await Employee.findOne({
      email: session.user.email,
      organization: orgContext.organization._id
    });
    if (!fromEmployeeRecord) {
      return NextResponse.json(
        { error: 'Employee record not found' },
        { status: 404 }
      );
    }

    // Validate that toEmployee exists (within same organization)
    const toEmployeeRecord = await Employee.findOne({
      _id: toEmployee,
      organization: orgContext.organization._id
    });
    if (!toEmployeeRecord) {
      return NextResponse.json(
        { error: 'Target employee not found' },
        { status: 404 }
      );
    }

    // Prevent self-thanks
    if (fromEmployeeRecord._id.toString() === toEmployee) {
      return NextResponse.json(
        { error: 'Cannot send thanks to yourself' },
        { status: 400 }
      );
    }

    const thanksData = {
      organization: orgContext.organization._id,
      fromEmployee: fromEmployeeRecord._id,
      toEmployee,
      message: message.trim(),
      category,
      isPublic,
      aiGenerated,
      reactions: []
    };

    const thanks = new Thanks(thanksData);
    await thanks.save();

    // Populate the response
    await thanks.populate('fromEmployee', 'name email image department position');
    await thanks.populate('toEmployee', 'name email image department position');

    // Create notification for the recipient
    try {
      await NotificationService.createThanksNotification(
        thanks._id,
        toEmployee,
        fromEmployeeRecord._id,
        fromEmployeeRecord.name,
        category,
        orgContext.organization._id
      );
    } catch (notificationError) {
      console.error('Error creating notification:', notificationError);
      // Don't fail the thanks creation if notification fails
    }

    return NextResponse.json(thanks, { status: 201 });

  } catch (error) {
    console.error('Error creating thanks:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
