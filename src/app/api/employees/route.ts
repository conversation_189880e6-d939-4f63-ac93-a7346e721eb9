import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Employee from '@/models/Employee';
import { withAuth, getAuthenticatedUser, canAccessUserData } from '@/lib/middleware/auth';
import { hasPermission } from '@/lib/roles';
import { getOrganizationFromRequest } from '@/lib/middleware/organization';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    // Check if user is authenticated
    const session = await getServerSession(authOptions);

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const department = searchParams.get('department');
    const limit = parseInt(searchParams.get('limit') || '50');
    const page = parseInt(searchParams.get('page') || '1');

    let query: any = {
      isActive: true
    };

    // For authenticated users, get organization context
    if (session?.user?.email) {
      const orgContext = await getOrganizationFromRequest(request);
      if (orgContext) {
        query.organization = orgContext.organization._id;
      }
    }
    // For anonymous users, we'll show all active employees (or you could implement other logic)

    // Role-based filtering for authenticated users
    if (session?.user?.email) {
      try {
        const user = getAuthenticatedUser(request);
        if (user.role === 'employee') {
          // Employees can only see public profiles (basic info)
          // This could be enhanced to show only certain fields
        } else if (user.role === 'manager') {
          // Managers can see their team members + public profiles
          if (user.department) {
            query.$or = [
              { department: user.department },
              { /* public profiles criteria could go here */ }
            ];
          }
        }
        // HR and Admin can see all profiles (no additional filtering)
      } catch (error) {
        console.warn('Could not get authenticated user, treating as anonymous');
      }
    }
    // Anonymous users see all active employees with basic info only

    if (search) {
      const searchQuery = {
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { bio: { $regex: search, $options: 'i' } },
          { skills: { $in: [new RegExp(search, 'i')] } },
          { interests: { $in: [new RegExp(search, 'i')] } }
        ]
      };

      if (query.$or) {
        query = { $and: [query, searchQuery] };
      } else {
        query = searchQuery;
      }
    }

    if (department) {
      if (query.$and) {
        query.$and.push({ department: { $regex: department, $options: 'i' } });
      } else {
        query.department = { $regex: department, $options: 'i' };
      }
    }

    // Select fields based on authentication status and role
    let selectFields = '-__v -password';

    if (!session?.user?.email) {
      // Anonymous users see only basic public fields
      selectFields = 'name email image department position';
    } else {
      try {
        const user = getAuthenticatedUser(request);
        if (user.role === 'employee') {
          // Employees see limited fields for other employees
          selectFields = 'name email image bio department position skills interests location linkedIn github';
        }
      } catch (error) {
        // If we can't get user info, default to basic fields
        selectFields = 'name email image department position';
      }
    }

    const employees = await Employee.find(query)
      .select(selectFields)
      .limit(limit)
      .skip((page - 1) * limit)
      .sort({ name: 1 });

    const total = await Employee.countDocuments(query);

    return NextResponse.json({
      employees,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching employees:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export const POST = withAuth(async (request: NextRequest) => {
  try {
    await dbConnect();

    // Get organization context
    const orgContext = await getOrganizationFromRequest(request);
    if (!orgContext) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    const body = await request.json();
    const { name, email, bio, department, position, skills, interests, location, linkedIn, github, startDate } = body;

    // Validate required fields
    if (!name || !email) {
      return NextResponse.json(
        { error: 'Name and email are required' },
        { status: 400 }
      );
    }

    // Check if employee already exists within organization
    const existingEmployee = await Employee.findOne({
      email,
      organization: orgContext.organization._id
    });
    if (existingEmployee) {
      return NextResponse.json(
        { error: 'Employee with this email already exists in this organization' },
        { status: 409 }
      );
    }

    const employeeData: any = {
      organization: orgContext.organization._id,
      name,
      email,
      bio,
      department,
      position,
      location,
      linkedIn,
      github,
      isActive: true
    };

    if (skills && Array.isArray(skills)) {
      employeeData.skills = skills;
    }

    if (interests && Array.isArray(interests)) {
      employeeData.interests = interests;
    }

    if (startDate) {
      employeeData.startDate = new Date(startDate);
    }

    const employee = new Employee(employeeData);
    await employee.save();

    return NextResponse.json(employee, { status: 201 });

  } catch (error) {
    console.error('Error creating employee:', error);

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}, { permissions: ['create_employees'] });
