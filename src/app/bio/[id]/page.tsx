'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  User,
  Mail,
  MapPin,
  Calendar,
  Building,
  Briefcase,
  ExternalLink,
  Github,
  Linkedin,
  Heart,
  Star,
  Clock,
  ArrowLeft,
  Trophy,
  Award,
  Medal,
  Crown,
  Sparkles
} from 'lucide-react';
import Link from 'next/link';

interface Achievement {
  _id: string;
  title: string;
  description: string;
  type: 'milestone' | 'recognition' | 'skill' | 'project' | 'anniversary' | 'custom';
  category: string;
  dateEarned: string;
  badge: {
    icon: string;
    color: string;
    rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  };
  metadata?: {
    thanksCount?: number;
    projectName?: string;
    skillLevel?: string;
    yearsOfService?: number;
    [key: string]: any;
  };
}

interface Employee {
  _id: string;
  name: string;
  email: string;
  image?: string;
  bio?: string;
  department?: string;
  position?: string;
  startDate?: string;
  skills?: string[];
  interests?: string[];
  location?: string;
  linkedIn?: string;
  github?: string;
  achievements?: Achievement[];
  achievementCount?: number;
}

export default function PublicBioPage() {
  const params = useParams();
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  useEffect(() => {
    if (params.id) {
      fetchEmployee(params.id as string);
    }
  }, [params.id]);

  const getAchievementIcon = (type: string) => {
    switch (type) {
      case 'milestone': return <Trophy className="w-5 h-5" />;
      case 'recognition': return <Award className="w-5 h-5" />;
      case 'skill': return <Star className="w-5 h-5" />;
      case 'project': return <Medal className="w-5 h-5" />;
      case 'anniversary': return <Crown className="w-5 h-5" />;
      default: return <Sparkles className="w-5 h-5" />;
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'from-yellow-400 to-orange-500';
      case 'epic': return 'from-purple-500 to-pink-500';
      case 'rare': return 'from-blue-500 to-indigo-600';
      case 'uncommon': return 'from-green-500 to-emerald-600';
      default: return 'from-gray-400 to-gray-500';
    }
  };

  const getRarityBorder = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'border-yellow-400';
      case 'epic': return 'border-purple-500';
      case 'rare': return 'border-blue-500';
      case 'uncommon': return 'border-green-500';
      default: return 'border-gray-400';
    }
  };

  const fetchEmployee = async (id: string) => {
    try {
      const response = await fetch(`/api/employees/${id}`);
      if (response.ok) {
        const data = await response.json();
        setEmployee(data);
      } else {
        setError('Employee not found');
      }
    } catch (error) {
      console.error('Error fetching employee:', error);
      setError('Failed to load employee bio');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full"
        />
      </div>
    );
  }

  if (error || !employee) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Bio Not Found</h1>
          <p className="text-gray-600 mb-4">{error || 'This bio does not exist or is not available.'}</p>
          <Link 
            href="/"
            className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <ArrowLeft size={16} />
            <span>Go Home</span>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8 text-center"
        >
          <Link 
            href="/"
            className="inline-flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors mb-4"
          >
            <ArrowLeft size={16} />
            <span>Back to Home</span>
          </Link>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            achy.me
          </h1>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-8"
        >
          {/* Hero Profile Section */}
          <motion.div
            variants={itemVariants}
            className="relative bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-8 text-white overflow-hidden"
          >
            <div className="absolute inset-0 bg-black/10 rounded-3xl"></div>
            <div className="relative z-10 flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8">
              <div className="relative">
                {employee.image ? (
                  <motion.img
                    whileHover={{ scale: 1.05 }}
                    src={employee.image}
                    alt={employee.name}
                    className="w-32 h-32 rounded-full border-4 border-white/20 shadow-2xl object-cover"
                  />
                ) : (
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    className="w-32 h-32 rounded-full border-4 border-white/20 shadow-2xl bg-white/10 flex items-center justify-center"
                  >
                    <User size={48} className="text-white/70" />
                  </motion.div>
                )}
              </div>
              
              <div className="flex-1 text-center md:text-left">
                <h2 className="text-3xl font-bold mb-2">{employee.name}</h2>
                <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-6 text-white/90">
                  <div className="flex items-center justify-center md:justify-start space-x-2">
                    <Mail size={16} />
                    <span>{employee.email}</span>
                  </div>
                  {employee.position && (
                    <div className="flex items-center justify-center md:justify-start space-x-2">
                      <Briefcase size={16} />
                      <span>{employee.position}</span>
                    </div>
                  )}
                  {employee.location && (
                    <div className="flex items-center justify-center md:justify-start space-x-2">
                      <MapPin size={16} />
                      <span>{employee.location}</span>
                    </div>
                  )}
                </div>
                {employee.startDate && (
                  <div className="flex items-center justify-center md:justify-start space-x-2 mt-3 text-white/80">
                    <Calendar size={16} />
                    <span>Joined {new Date(employee.startDate).toLocaleDateString('en-US', { 
                      year: 'numeric', 
                      month: 'long' 
                    })}</span>
                  </div>
                )}
              </div>
            </div>
          </motion.div>

          {/* Bio Section */}
          {employee.bio && (
            <motion.div
              variants={itemVariants}
              className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8"
            >
              <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                  <User className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900">About</h3>
              </div>
              <p className="text-gray-700 leading-relaxed text-lg">{employee.bio}</p>
            </motion.div>
          )}

          {/* Info Cards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {employee.department && (
              <motion.div
                variants={itemVariants}
                className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Building className="w-5 h-5 text-blue-600" />
                  </div>
                  <h4 className="font-semibold text-gray-900">Department</h4>
                </div>
                <p className="text-gray-700 text-lg">{employee.department}</p>
              </motion.div>
            )}

            {employee.startDate && (
              <motion.div
                variants={itemVariants}
                className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Clock className="w-5 h-5 text-green-600" />
                  </div>
                  <h4 className="font-semibold text-gray-900">Tenure</h4>
                </div>
                <p className="text-gray-700 text-lg">
                  {Math.floor((new Date().getTime() - new Date(employee.startDate).getTime()) / (1000 * 60 * 60 * 24 * 365))} years
                </p>
              </motion.div>
            )}
          </div>

          {/* Skills Section */}
          {employee.skills && employee.skills.length > 0 && (
            <motion.div
              variants={itemVariants}
              className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8"
            >
              <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg">
                  <Star className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900">Skills & Expertise</h3>
              </div>
              <div className="flex flex-wrap gap-3">
                {employee.skills.map((skill, index) => (
                  <motion.span
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium shadow-md"
                  >
                    {skill}
                  </motion.span>
                ))}
              </div>
            </motion.div>
          )}

          {/* Interests Section */}
          {employee.interests && employee.interests.length > 0 && (
            <motion.div
              variants={itemVariants}
              className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8"
            >
              <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-gradient-to-r from-pink-500 to-rose-600 rounded-lg">
                  <Heart className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900">Interests & Hobbies</h3>
              </div>
              <div className="flex flex-wrap gap-3">
                {employee.interests.map((interest, index) => (
                  <motion.span
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-4 py-2 rounded-full text-sm font-medium shadow-md"
                  >
                    {interest}
                  </motion.span>
                ))}
              </div>
            </motion.div>
          )}

          {/* Achievements Section */}
          {employee.achievements && employee.achievements.length > 0 && (
            <motion.div
              variants={itemVariants}
              className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8"
            >
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg">
                    <Trophy className="w-5 h-5 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">Achievements</h3>
                </div>
                <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  {employee.achievementCount || employee.achievements.length} Total
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {employee.achievements.map((achievement, index) => (
                  <motion.div
                    key={achievement._id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ scale: 1.02, y: -2 }}
                    className={`relative bg-gradient-to-br from-white to-gray-50 rounded-xl p-6 border-2 ${getRarityBorder(achievement.badge.rarity)} shadow-md hover:shadow-lg transition-all duration-200`}
                  >
                    {/* Rarity indicator */}
                    <div className={`absolute top-2 right-2 w-3 h-3 rounded-full bg-gradient-to-r ${getRarityColor(achievement.badge.rarity)}`}></div>

                    <div className="flex items-start space-x-4">
                      <div className={`p-3 rounded-xl bg-gradient-to-r ${getRarityColor(achievement.badge.rarity)} text-white shadow-md`}>
                        {getAchievementIcon(achievement.type)}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="font-semibold text-gray-900 truncate">{achievement.title}</h4>
                          <span className="text-lg">{achievement.badge.icon}</span>
                        </div>

                        <p className="text-sm text-gray-600 mb-3 line-clamp-2">{achievement.description}</p>

                        <div className="flex items-center justify-between">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r ${getRarityColor(achievement.badge.rarity)} text-white`}>
                            {achievement.category}
                          </span>
                          <span className="text-xs text-gray-500">
                            {new Date(achievement.dateEarned).toLocaleDateString('en-US', {
                              month: 'short',
                              year: 'numeric'
                            })}
                          </span>
                        </div>

                        {/* Metadata */}
                        {achievement.metadata && (
                          <div className="mt-3 flex flex-wrap gap-2">
                            {achievement.metadata.thanksCount && (
                              <span className="text-xs bg-pink-100 text-pink-700 px-2 py-1 rounded-full">
                                {achievement.metadata.thanksCount} Thanks
                              </span>
                            )}
                            {achievement.metadata.skillLevel && (
                              <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                                {achievement.metadata.skillLevel}
                              </span>
                            )}
                            {achievement.metadata.yearsOfService && (
                              <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                                {achievement.metadata.yearsOfService} Years
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Achievement Summary */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {['milestone', 'recognition', 'skill', 'project'].map((type) => {
                    const count = employee.achievements?.filter(a => a.type === type).length || 0;
                    return count > 0 ? (
                      <div key={type} className="text-center">
                        <div className="flex justify-center mb-2">
                          {getAchievementIcon(type)}
                        </div>
                        <div className="text-lg font-bold text-gray-900">{count}</div>
                        <div className="text-xs text-gray-600 capitalize">{type}s</div>
                      </div>
                    ) : null;
                  })}
                </div>
              </div>
            </motion.div>
          )}

          {/* Social Links */}
          {(employee.linkedIn || employee.github) && (
            <motion.div
              variants={itemVariants}
              className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8"
            >
              <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg">
                  <ExternalLink className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900">Connect</h3>
              </div>
              <div className="flex space-x-4">
                {employee.linkedIn && (
                  <motion.a
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    href={employee.linkedIn}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <Linkedin size={18} />
                    <span>LinkedIn</span>
                  </motion.a>
                )}
                {employee.github && (
                  <motion.a
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    href={employee.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-2 bg-gray-800 text-white px-4 py-2 rounded-lg hover:bg-gray-900 transition-colors"
                  >
                    <Github size={18} />
                    <span>GitHub</span>
                  </motion.a>
                )}
              </div>
            </motion.div>
          )}
        </motion.div>
      </div>
    </div>
  );
}
