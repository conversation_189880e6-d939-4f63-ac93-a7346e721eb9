'use client';

import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  User,
  Mail,
  MapPin,
  Calendar,
  Building,
  Briefcase,
  Edit3,
  Save,
  X,
  Camera,
  ExternalLink,
  Github,
  Linkedin,
  Heart,
  Star,
  Award,
  Clock,
  Share2,
  Copy,
  Code,
  Link,
  Check
} from 'lucide-react';
import Navigation from '@/components/Navigation';
import ImageUpload from '@/components/ImageUpload';

interface Employee {
  _id: string;
  name: string;
  email: string;
  image?: string;
  bio?: string;
  department?: string;
  position?: string;
  startDate?: string;
  skills?: string[];
  interests?: string[];
  location?: string;
  linkedIn?: string;
  github?: string;
}

export default function ProfilePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string | null>(null);
  const [showShareModal, setShowShareModal] = useState(false);
  const [copied, setCopied] = useState(false);
  const [embedCopied, setEmbedCopied] = useState(false);
  const [instructionsCopied, setInstructionsCopied] = useState(false);
  const [signatureTab, setSignatureTab] = useState<'html' | 'manual'>('html');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    } else if (status === 'authenticated' && session?.user?.email) {
      fetchEmployee();
    }
  }, [status, session?.user?.email, router]);

  const fetchEmployee = async () => {
    try {
      const response = await fetch('/api/employees/me');
      if (response.ok) {
        const data = await response.json();
        setEmployee(data);
      } else {
        console.error('Failed to fetch employee:', response.status);
        if (response.status === 404) {
          // Employee record doesn't exist yet, this is normal for new users
          setEmployee(null);
        }
      }
    } catch (error) {
      console.error('Error fetching employee:', error);
      setEmployee(null);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (formData: FormData) => {
    setSaving(true);
    try {
      // Add the uploaded image URL to the form data if it exists
      const imageUrl = uploadedImageUrl || employee?.image;
      if (imageUrl) {
        formData.append('currentImageUrl', imageUrl);
      }

      const response = await fetch('/api/employees/me', {
        method: 'PUT',
        body: formData,
      });

      if (response.ok) {
        const updatedEmployee = await response.json();
        setEmployee(updatedEmployee);
        setIsEditing(false);
        setUploadedImageUrl(null); // Reset uploaded image state
      } else {
        const errorData = await response.json();
        alert(errorData.error || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Error updating profile');
    } finally {
      setSaving(false);
    }
  };

  const getShareableUrl = () => {
    if (!employee?._id) return '';
    return `${window.location.origin}/bio/${employee._id}`;
  };

  const getEmbedCode = () => {
    const shareUrl = getShareableUrl();
    const thanksUrl = `${window.location.origin}/thanks?to=${employee?._id}`;
    if (!employee) return '';

    // Simple Outlook-compatible signature
    return `<div style="font-family: Arial, sans-serif; font-size: 14px; color: #333;">
<p style="margin: 0; padding: 0;">
<strong style="font-size: 16px; color: #1f2937;">${employee.name}</strong><br>
${employee.position ? `<span style="color: #6b7280;">${employee.position}</span><br>` : ''}
${employee.department ? `<span style="color: #6b7280;">${employee.department}</span><br>` : ''}
<a href="mailto:${employee.email}" style="color: #3b82f6; text-decoration: none;">${employee.email}</a><br>
${employee.location ? `<span style="color: #6b7280;">📍 ${employee.location}</span><br>` : ''}
<br>
<a href="${shareUrl}" style="color: #3b82f6; text-decoration: none;">🔗 View My Bio</a> | <a href="${thanksUrl}" style="color: #e91e63; text-decoration: none;">💝 Send Thanks</a>
${employee.linkedIn ? ` | <a href="${employee.linkedIn}" style="color: #0077b5; text-decoration: none;">LinkedIn</a>` : ''}
${employee.github ? ` | <a href="${employee.github}" style="color: #333; text-decoration: none;">GitHub</a>` : ''}
</p>
</div>`;
  };

  const getOutlookInstructions = () => {
    if (!employee) return '';
    const thanksUrl = `${window.location.origin}/thanks?to=${employee._id}`;

    return `Manual Outlook Signature Setup:

1. Open Outlook → File → Options → Mail → Signatures
2. Click "New" to create a new signature
3. In the signature editor, type the following:

${employee.name}
${employee.position || ''}
${employee.department || ''}
${employee.email}
${employee.location ? `📍 ${employee.location}` : ''}

🔗 View My Bio: ${getShareableUrl()}
💝 Send Thanks: ${thanksUrl}
${employee.linkedIn ? `LinkedIn: ${employee.linkedIn}` : ''}
${employee.github ? `GitHub: ${employee.github}` : ''}

4. Select your name and make it bold and larger
5. Select email and make it a hyperlink (Ctrl+K)
6. Select "View My Bio" link and make it a hyperlink
7. Select "Send Thanks" link and make it a hyperlink
8. Select social media links and make them hyperlinks
9. Save and apply to your emails`;
  };

  const copyToClipboard = async (text: string, type: 'link' | 'embed' | 'instructions') => {
    try {
      await navigator.clipboard.writeText(text);
      if (type === 'link') {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } else if (type === 'embed') {
        setEmbedCopied(true);
        setTimeout(() => setEmbedCopied(false), 2000);
      } else {
        setInstructionsCopied(true);
        setTimeout(() => setInstructionsCopied(false), 2000);
      }
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const shareViaWebShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${employee?.name || 'Employee'} - Bio`,
          text: `Check out ${employee?.name || 'this employee'}'s bio`,
          url: getShareableUrl(),
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      // Fallback to copy link
      copyToClipboard(getShareableUrl(), 'link');
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <Navigation />
        <div className="max-w-6xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex flex-col items-center justify-center h-64"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full mb-4"
            />
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="text-gray-600 font-medium"
            >
              Loading your profile...
            </motion.p>
          </motion.div>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <Navigation />

      <div className="max-w-6xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                My Profile
              </h1>
              <p className="mt-2 text-gray-600 text-lg">
                Manage your personal information and bio
              </p>
            </div>
            <div className="flex items-center space-x-3">
              {employee && !isEditing && (
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setShowShareModal(true)}
                  className="flex items-center space-x-2 px-4 py-3 bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-200 border border-gray-200 text-gray-700 font-medium"
                >
                  <Share2 size={18} />
                  <span>Share Bio</span>
                </motion.button>
              )}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  if (isEditing) {
                    // Reset uploaded image state when canceling
                    setUploadedImageUrl(null);
                    // Refresh employee data to get the original state
                    fetchEmployee();
                  }
                  setIsEditing(!isEditing);
                }}
                className={`flex items-center space-x-2 px-6 py-3 rounded-xl shadow-lg transition-all duration-200 font-medium ${
                  isEditing
                    ? 'bg-gray-600 hover:bg-gray-700 text-white'
                    : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:shadow-xl text-white'
                }`}
              >
                {isEditing ? (
                  <>
                    <X size={18} />
                    <span>Cancel</span>
                  </>
                ) : (
                  <>
                    <Edit3 size={18} />
                    <span>Edit Profile</span>
                  </>
                )}
              </motion.button>
            </div>
          </div>
        </motion.div>

        <AnimatePresence mode="wait">
          {isEditing ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="bg-white rounded-3xl shadow-xl border border-gray-100 p-8"
            >
              <div className="flex items-center space-x-3 mb-8">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                  <Edit3 className="w-5 h-5 text-white" />
                </div>
                <h2 className="text-2xl font-semibold text-gray-900">Edit Profile</h2>
              </div>

              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  const formData = new FormData(e.currentTarget);
                  handleSave(formData);
                }}
                className="space-y-8"
              >
                {/* Basic Information */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                    <User size={20} className="text-blue-600" />
                    <span>Basic Information</span>
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Full Name
                      </label>
                      <input
                        type="text"
                        name="name"
                        defaultValue={employee?.name || session?.user?.name || ''}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Department
                      </label>
                      <input
                        type="text"
                        name="department"
                        defaultValue={employee?.department || ''}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900"
                        placeholder="e.g., Engineering, Marketing"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Position
                      </label>
                      <input
                        type="text"
                        name="position"
                        defaultValue={employee?.position || ''}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        placeholder="e.g., Senior Developer, Product Manager"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Location
                      </label>
                      <input
                        type="text"
                        name="location"
                        defaultValue={employee?.location || ''}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        placeholder="e.g., San Francisco, CA"
                      />
                    </div>
                  </div>
                </div>

                {/* Profile Image Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                    <Camera size={20} className="text-green-600" />
                    <span>Profile Image</span>
                  </h3>

                  <div className="flex flex-col items-center space-y-4">
                    <ImageUpload
                      currentImage={uploadedImageUrl || employee?.image}
                      onImageUpload={(imageUrl) => {
                        setUploadedImageUrl(imageUrl);
                        // Only update the uploaded image URL; do not update employee state here to avoid form reset
                      }}
                      onImageRemove={() => {
                        setUploadedImageUrl(null);
                        // Only clear the uploaded image URL; do not update employee state here
                      }}
                      size="lg"
                      disabled={saving}
                    />
                    <div className="text-center">
                      <p className="text-sm text-gray-600 mb-1">
                        Upload a profile image (max 10MB)
                      </p>
                      <p className="text-xs text-gray-500">
                        Images will be automatically compressed to under 100KB for optimal performance
                      </p>
                      {uploadedImageUrl && (
                        <div className="mt-2 flex items-center justify-center space-x-1 text-xs text-blue-600 bg-blue-50 rounded-lg px-3 py-1">
                          <span>✓</span>
                          <span>Image uploaded! Remember to save your profile to apply changes.</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Bio Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                    <Edit3 size={20} className="text-purple-600" />
                    <span>About You</span>
                  </h3>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Bio
                    </label>
                    <textarea
                      name="bio"
                      rows={5}
                      defaultValue={employee?.bio || ''}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 resize-none text-gray-900"
                      placeholder="Tell us about yourself, your background, and what you're passionate about..."
                    />
                  </div>
                </div>

                {/* Skills & Interests */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                    <Star size={20} className="text-yellow-600" />
                    <span>Skills & Interests</span>
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Skills (comma-separated)
                      </label>
                      <input
                        type="text"
                        name="skills"
                        defaultValue={employee?.skills?.join(', ') || ''}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-200 text-gray-900"
                        placeholder="JavaScript, React, Node.js, Python"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Interests (comma-separated)
                      </label>
                      <input
                        type="text"
                        name="interests"
                        defaultValue={employee?.interests?.join(', ') || ''}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-gray-900"
                        placeholder="Photography, Hiking, Reading, Gaming"
                      />
                    </div>
                  </div>
                </div>

                {/* Professional Details */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                    <Briefcase size={20} className="text-indigo-600" />
                    <span>Professional Details</span>
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Start Date
                      </label>
                      <input
                        type="date"
                        name="startDate"
                        defaultValue={employee?.startDate ? new Date(employee.startDate).toISOString().split('T')[0] : ''}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 text-gray-900"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        GitHub Profile
                      </label>
                      <input
                        type="url"
                        name="github"
                        defaultValue={employee?.github || ''}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent transition-all duration-200 text-gray-900"
                        placeholder="https://github.com/username"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      LinkedIn Profile
                    </label>
                    <input
                      type="url"
                      name="linkedIn"
                      defaultValue={employee?.linkedIn || ''}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900"
                      placeholder="https://linkedin.com/in/username"
                    />
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    type="button"
                    onClick={() => {
                      setUploadedImageUrl(null);
                      // Refresh employee data to get the original state
                      fetchEmployee();
                      setIsEditing(false);
                    }}
                    className="px-6 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium"
                  >
                    Cancel
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    type="submit"
                    disabled={saving}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:shadow-lg text-white px-8 py-3 rounded-xl transition-all duration-200 disabled:opacity-50 font-medium flex items-center space-x-2"
                  >
                    {saving ? (
                      <>
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                        />
                        <span>Saving...</span>
                      </>
                    ) : (
                      <>
                        <Save size={18} />
                        <span>Save Changes</span>
                      </>
                    )}
                  </motion.button>
                </div>
              </form>
            </motion.div>
            ) : (
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                className="space-y-8"
              >
                {/* Hero Profile Section */}
                <motion.div
                  variants={itemVariants}
                  className="relative bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-8 text-white overflow-hidden"
                >
                  <div className="absolute inset-0 bg-black/10 rounded-3xl"></div>
                  <div className="relative z-10 flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8">
                    <div className="relative">
                      <ImageUpload
                        currentImage={employee?.image || session?.user?.image}
                        onImageUpload={async (imageUrl) => {
                          // Immediately update the employee image on the server using FormData
                          if (!employee) return;
                          const formData = new FormData();
                          formData.append('currentImageUrl', imageUrl);
                          const response = await fetch('/api/employees/me', {
                            method: 'PUT',
                            body: formData,
                          });
                          if (response.ok) {
                            // Always fetch the latest employee data to get the correct R2 image URL
                            const latest = await fetch('/api/employees/me');
                            if (latest.ok) {
                              const latestEmployee = await latest.json();
                              setEmployee(latestEmployee);
                            }
                          }
                        }}
                        onImageRemove={async () => {
                          // Immediately remove the employee image on the server using FormData
                          if (!employee) return;
                          const formData = new FormData();
                          formData.append('currentImageUrl', '');
                          const response = await fetch('/api/employees/me', {
                            method: 'PUT',
                            body: formData,
                          });
                          if (response.ok) {
                            // Always fetch the latest employee data to get the correct state
                            const latest = await fetch('/api/employees/me');
                            if (latest.ok) {
                              const latestEmployee = await latest.json();
                              setEmployee(latestEmployee);
                            }
                          }
                        }}
                        size="lg"
                        disabled={false}
                      />
                    </div>

                    <div className="flex-1 text-center md:text-left">
                      <h2 className="text-3xl font-bold mb-2">
                        {employee?.name || session?.user?.name || 'Welcome!'}
                      </h2>
                      <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-6 text-white/90">
                        <div className="flex items-center justify-center md:justify-start space-x-2">
                          <Mail size={16} />
                          <span>{employee?.email || session?.user?.email}</span>
                        </div>
                        {employee?.position && (
                          <div className="flex items-center justify-center md:justify-start space-x-2">
                            <Briefcase size={16} />
                            <span>{employee.position}</span>
                          </div>
                        )}
                        {employee?.location && (
                          <div className="flex items-center justify-center md:justify-start space-x-2">
                            <MapPin size={16} />
                            <span>{employee.location}</span>
                          </div>
                        )}
                      </div>
                      {employee?.startDate && (
                        <div className="flex items-center justify-center md:justify-start space-x-2 mt-3 text-white/80">
                          <Calendar size={16} />
                          <span>Joined {new Date(employee.startDate).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long'
                          })}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>

                {/* Bio Section */}
                {employee?.bio && (
                  <motion.div
                    variants={itemVariants}
                    className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8"
                  >
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                        <User className="w-5 h-5 text-white" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900">About Me</h3>
                    </div>
                    <p className="text-gray-700 leading-relaxed text-lg">{employee.bio}</p>
                  </motion.div>
                )}

                {/* Info Cards Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {employee?.department && (
                    <motion.div
                      variants={itemVariants}
                      whileHover={{ scale: 1.02, y: -5 }}
                      className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300"
                    >
                      <div className="flex items-center space-x-3 mb-4">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <Building className="w-5 h-5 text-blue-600" />
                        </div>
                        <h4 className="font-semibold text-gray-900">Department</h4>
                      </div>
                      <p className="text-gray-700 text-lg">{employee.department}</p>
                    </motion.div>
                  )}

                  {employee?.startDate && (
                    <motion.div
                      variants={itemVariants}
                      whileHover={{ scale: 1.02, y: -5 }}
                      className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300"
                    >
                      <div className="flex items-center space-x-3 mb-4">
                        <div className="p-2 bg-green-100 rounded-lg">
                          <Clock className="w-5 h-5 text-green-600" />
                        </div>
                        <h4 className="font-semibold text-gray-900">Tenure</h4>
                      </div>
                      <p className="text-gray-700 text-lg">
                        {Math.floor((new Date().getTime() - new Date(employee.startDate).getTime()) / (1000 * 60 * 60 * 24 * 365))} years
                      </p>
                    </motion.div>
                  )}
                </div>

                {/* Skills Section */}
                {employee?.skills && employee.skills.length > 0 && (
                  <motion.div
                    variants={itemVariants}
                    className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8"
                  >
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="p-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg">
                        <Star className="w-5 h-5 text-white" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900">Skills & Expertise</h3>
                    </div>
                    <div className="flex flex-wrap gap-3">
                      {employee.skills.map((skill, index) => (
                        <motion.span
                          key={index}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: index * 0.1 }}
                          whileHover={{ scale: 1.05 }}
                          className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200"
                        >
                          {skill}
                        </motion.span>
                      ))}
                    </div>
                  </motion.div>
                )}

                {/* Interests Section */}
                {employee?.interests && employee.interests.length > 0 && (
                  <motion.div
                    variants={itemVariants}
                    className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8"
                  >
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="p-2 bg-gradient-to-r from-pink-500 to-rose-600 rounded-lg">
                        <Heart className="w-5 h-5 text-white" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900">Interests & Hobbies</h3>
                    </div>
                    <div className="flex flex-wrap gap-3">
                      {employee.interests.map((interest, index) => (
                        <motion.span
                          key={index}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: index * 0.1 }}
                          whileHover={{ scale: 1.05 }}
                          className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-4 py-2 rounded-full text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200"
                        >
                          {interest}
                        </motion.span>
                      ))}
                    </div>
                  </motion.div>
                )}

                {/* Social Links */}
                {(employee?.linkedIn || employee?.github) && (
                  <motion.div
                    variants={itemVariants}
                    className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8"
                  >
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="p-2 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg">
                        <ExternalLink className="w-5 h-5 text-white" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900">Connect With Me</h3>
                    </div>
                    <div className="flex space-x-4">
                      {employee.linkedIn && (
                        <motion.a
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          href={employee.linkedIn}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          <Linkedin size={18} />
                          <span>LinkedIn</span>
                        </motion.a>
                      )}
                      {employee.github && (
                        <motion.a
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          href={employee.github}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center space-x-2 bg-gray-800 text-white px-4 py-2 rounded-lg hover:bg-gray-900 transition-colors"
                        >
                          <Github size={18} />
                          <span>GitHub</span>
                        </motion.a>
                      )}
                    </div>
                  </motion.div>
                )}
              </motion.div>
            )}
        </AnimatePresence>

        {/* Share Modal */}
        <AnimatePresence>
          {showShareModal && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={() => setShowShareModal(false)}
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.95, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: 20 }}
                transition={{ duration: 0.2 }}
                className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                      <Share2 className="w-5 h-5 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900">Share Your Bio</h3>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setShowShareModal(false)}
                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <X size={20} className="text-gray-500" />
                  </motion.button>
                </div>

                <div className="space-y-6">
                  {/* Share Link */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      <Link size={16} className="inline mr-2" />
                      Shareable Link
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={getShareableUrl()}
                        readOnly
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-sm text-gray-900"
                      />
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => copyToClipboard(getShareableUrl(), 'link')}
                        className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                          copied
                            ? 'bg-green-100 text-green-700'
                            : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                        }`}
                      >
                        {copied ? <Check size={16} /> : <Copy size={16} />}
                      </motion.button>
                    </div>
                    <p className="text-xs text-gray-500 mt-2">
                      Share this link to let others view your bio
                    </p>
                  </div>

                  {/* Outlook Signature */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      <Code size={16} className="inline mr-2" />
                      Outlook Email Signature
                    </label>

                    {/* Tab Navigation */}
                    <div className="flex mb-3 bg-gray-100 rounded-lg p-1">
                      <button
                        onClick={() => setSignatureTab('html')}
                        className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                          signatureTab === 'html'
                            ? 'bg-white text-blue-700 shadow-sm'
                            : 'text-gray-600 hover:text-gray-800'
                        }`}
                      >
                        Simple HTML
                      </button>
                      <button
                        onClick={() => setSignatureTab('manual')}
                        className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                          signatureTab === 'manual'
                            ? 'bg-white text-blue-700 shadow-sm'
                            : 'text-gray-600 hover:text-gray-800'
                        }`}
                      >
                        Manual Setup
                      </button>
                    </div>

                    {signatureTab === 'html' ? (
                      <div className="space-y-2">
                        <textarea
                          value={getEmbedCode()}
                          readOnly
                          rows={8}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-xs font-mono resize-none text-gray-900"
                        />
                        <motion.button
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => copyToClipboard(getEmbedCode(), 'embed')}
                          className={`w-full px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                            embedCopied
                              ? 'bg-green-100 text-green-700'
                              : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                          }`}
                        >
                          {embedCopied ? (
                            <>
                              <Check size={16} className="inline mr-2" />
                              Copied HTML!
                            </>
                          ) : (
                            <>
                              <Copy size={16} className="inline mr-2" />
                              Copy HTML Code
                            </>
                          )}
                        </motion.button>
                        <p className="text-xs text-gray-600">
                          ⚠️ Some Outlook versions may not support HTML. Try Manual Setup if this doesn't work.
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <textarea
                          value={getOutlookInstructions()}
                          readOnly
                          rows={12}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-xs resize-none text-gray-900"
                        />
                        <motion.button
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => copyToClipboard(getOutlookInstructions(), 'instructions')}
                          className={`w-full px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                            instructionsCopied
                              ? 'bg-green-100 text-green-700'
                              : 'bg-orange-100 text-orange-700 hover:bg-orange-200'
                          }`}
                        >
                          {instructionsCopied ? (
                            <>
                              <Check size={16} className="inline mr-2" />
                              Copied Instructions!
                            </>
                          ) : (
                            <>
                              <Copy size={16} className="inline mr-2" />
                              Copy Instructions
                            </>
                          )}
                        </motion.button>
                        <p className="text-xs text-gray-600">
                          ✅ This method works with all Outlook versions and is the most reliable.
                        </p>
                      </div>
                    )}
                    {/* Signature Preview */}
                    {signatureTab === 'html' && (
                      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mt-3">
                        <p className="text-xs text-gray-700 font-medium mb-3">👀 Preview:</p>
                        <div
                          className="bg-white p-3 rounded border"
                          dangerouslySetInnerHTML={{ __html: getEmbedCode() }}
                        />
                      </div>
                    )}

                    {/* Instructions */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-3">
                      <p className="text-xs text-blue-700 font-medium mb-2">📧 How to Add to Outlook:</p>
                      {signatureTab === 'html' ? (
                        <ol className="text-xs text-blue-600 space-y-1 ml-4 list-decimal">
                          <li>Copy the HTML code above</li>
                          <li>Open Outlook → File → Options → Mail → Signatures</li>
                          <li>Create new signature or edit existing</li>
                          <li>Paste the HTML code directly into the editor</li>
                          <li>Save and apply to your emails</li>
                          <li>If HTML doesn't work, try "Manual Setup" tab</li>
                        </ol>
                      ) : (
                        <ol className="text-xs text-blue-600 space-y-1 ml-4 list-decimal">
                          <li>Copy the instructions above</li>
                          <li>Follow the step-by-step guide</li>
                          <li>Type the information manually in Outlook</li>
                          <li>Format text as needed (bold, links, etc.)</li>
                          <li>This method works with all Outlook versions</li>
                        </ol>
                      )}
                    </div>
                  </div>

                  {/* Social Share */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Quick Share
                    </label>
                    <div className="flex space-x-2">
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={shareViaWebShare}
                        className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:shadow-lg transition-all duration-200"
                      >
                        <Share2 size={16} />
                        <span>Share</span>
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => {
                          const url = `https://twitter.com/intent/tweet?text=Check out my bio&url=${encodeURIComponent(getShareableUrl())}`;
                          window.open(url, '_blank');
                        }}
                        className="px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                      >
                        <ExternalLink size={16} />
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => {
                          const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(getShareableUrl())}`;
                          window.open(url, '_blank');
                        }}
                        className="px-4 py-3 bg-blue-700 text-white rounded-lg hover:bg-blue-800 transition-colors"
                      >
                        <Linkedin size={16} />
                      </motion.button>
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
