'use client';

import { useEffect, useState } from 'react';

interface ImageDebuggerProps {
  imageUrl?: string;
  label?: string;
}

export default function ImageDebugger({ imageUrl, label = "Image" }: ImageDebuggerProps) {
  const [imageStatus, setImageStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorDetails, setErrorDetails] = useState<string>('');

  useEffect(() => {
    if (!imageUrl) {
      setImageStatus('error');
      setErrorDetails('No image URL provided');
      return;
    }

    setImageStatus('loading');

    // Test if the image can be loaded
    const img = new Image();

    img.onload = () => {
      setImageStatus('success');
      setErrorDetails('');
    };

    img.onerror = (error) => {
      setImageStatus('error');
      setErrorDetails(`Failed to load image. This might be due to:
      1. R2 bucket not configured for public access
      2. CORS settings not configured
      3. Image doesn't exist in R2
      4. Network connectivity issues`);
    };

    // Add crossOrigin to test CORS
    img.crossOrigin = 'anonymous';
    img.src = imageUrl;
  }, [imageUrl]);

  if (!imageUrl) {
    return (
      <div className="p-2 bg-yellow-50 border border-yellow-200 rounded text-xs">
        <strong>{label}:</strong> No URL provided
      </div>
    );
  }

  return (
    <div className="p-2 bg-gray-50 border border-gray-200 rounded text-xs space-y-1">
      <div><strong>{label} URL:</strong></div>
      <div className="break-all text-blue-600">{imageUrl}</div>
      <div className="flex items-center space-x-2">
        <span><strong>Status:</strong></span>
        <span className={`px-2 py-1 rounded text-xs ${
          imageStatus === 'success' ? 'bg-green-100 text-green-800' :
          imageStatus === 'error' ? 'bg-red-100 text-red-800' :
          'bg-yellow-100 text-yellow-800'
        }`}>
          {imageStatus}
        </span>
      </div>
      {errorDetails && (
        <div className="text-red-600 text-xs whitespace-pre-line">{errorDetails}</div>
      )}
      {imageStatus === 'error' && imageUrl?.includes('r2.dev') && (
        <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
          <strong>R2 Setup Required:</strong>
          <ol className="mt-1 ml-4 list-decimal text-xs">
            <li>Go to Cloudflare Dashboard → R2 Object Storage</li>
            <li>Select your bucket: <code className="bg-gray-100 px-1 rounded">bio</code></li>
            <li>Go to Settings → Public Access</li>
            <li>Enable "Allow Access" for public reads</li>
            <li>Configure custom domain if needed</li>
          </ol>
        </div>
      )}
      {imageStatus === 'success' && (
        <div className="mt-2">
          <img
            src={imageUrl}
            alt="Debug preview"
            className="w-16 h-16 object-cover rounded border"
            onError={() => setImageStatus('error')}
          />
        </div>
      )}
    </div>
  );
}
