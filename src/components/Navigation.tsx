'use client';

import { useSession, signOut } from 'next-auth/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  User,
  Heart,
  Trophy,
  BarChart3,
  Users,
  Settings,
  LogOut,
  Menu,
  X,
  ChevronDown,
  Bell,
  Search
} from 'lucide-react';
import { hasPermission } from '@/lib/roles';
import { UserRole } from '@/types/global';
import NotificationBell from '@/components/NotificationBell';

export default function Navigation() {
  const { data: session, status } = useSession();
  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);

  if (status === 'loading') {
    return (
      <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="h-8 w-32 bg-gradient-to-r from-gray-200 to-gray-300 animate-pulse rounded-lg"
              />
            </div>
            <div className="flex items-center space-x-3">
              <div className="h-8 w-20 bg-gray-200 animate-pulse rounded-lg"></div>
            </div>
          </div>
        </div>
      </nav>
    );
  }

  if (!session) {
    return (
      <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="flex items-center"
            >
              <Link href="/" className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Employee Bio
              </Link>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="flex items-center space-x-3"
            >
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  href="/auth/signin"
                  className="text-gray-700 hover:text-gray-900 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-gray-100"
                >
                  Sign In
                </Link>
              </motion.div>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  href="/auth/signup"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:shadow-lg transition-all duration-200"
                >
                  Sign Up
                </Link>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </nav>
    );
  }

  const userRole = session.user?.role as UserRole || 'employee';

  // Helper function to get navigation icons
  const getNavIcon = (name: string) => {
    switch (name) {
      case 'Profile': return <User size={18} />;
      case 'Thanks': return <Heart size={18} />;
      case 'Achievements': return <Trophy size={18} />;
      case 'Team Analytics': return <BarChart3 size={18} />;
      case 'HR Dashboard': return <Users size={18} />;
      case 'Admin Panel': return <Settings size={18} />;
      default: return null;
    }
  };

  // Build navigation based on user role and permissions
  const navigation = [
    {
      name: 'Profile',
      href: '/profile',
      current: pathname === '/profile',
      icon: getNavIcon('Profile'),
      description: 'Manage your profile'
    },
    {
      name: 'Thanks',
      href: '/thanks',
      current: pathname === '/thanks',
      icon: getNavIcon('Thanks'),
      description: 'Send and receive thanks'
    },
    {
      name: 'Achievements',
      href: '/achievements',
      current: pathname === '/achievements',
      icon: getNavIcon('Achievements'),
      description: 'View your achievements'
    },
  ];

  // Add role-specific navigation items
  if (hasPermission(userRole, 'view_team_analytics')) {
    navigation.push({
      name: 'Team Analytics',
      href: '/analytics/team',
      current: pathname === '/analytics/team',
      icon: getNavIcon('Team Analytics'),
      description: 'Team performance insights'
    });
  }

  if (hasPermission(userRole, 'view_hr_analytics')) {
    navigation.push({
      name: 'HR Dashboard',
      href: '/hr',
      current: pathname === '/hr',
      icon: getNavIcon('HR Dashboard'),
      description: 'HR management tools'
    });
  }

  if (hasPermission(userRole, 'manage_users')) {
    navigation.push({
      name: 'Admin Panel',
      href: '/admin',
      current: pathname.startsWith('/admin'),
      icon: getNavIcon('Admin Panel'),
      description: 'System administration'
    });
  }

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800 border-red-200';
      case 'hr': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'manager': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <nav className="bg-white/95 backdrop-blur-lg border-b border-gray-200/50 sticky top-0 z-50 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Left side - Logo */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex-shrink-0 flex items-center space-x-3"
          >
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <User className="w-5 h-5 text-white" />
            </div>
            <Link href="/" className="text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent whitespace-nowrap">
              Employee Bio
            </Link>
          </motion.div>

          {/* Center - Desktop Navigation */}
          <div className="hidden lg:flex lg:items-center lg:space-x-1 flex-1 justify-center max-w-2xl mx-8">
            {navigation.map((item, index) => (
              <motion.div
                key={item.name}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -1 }}
                className="relative"
              >
                <Link
                  href={item.href}
                  className={`${
                    item.current
                      ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-md'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  } flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 whitespace-nowrap`}
                >
                  <span className="mr-2">{item.icon}</span>
                  <span>{item.name}</span>
                </Link>
              </motion.div>
            ))}
          </div>
          {/* Right side - User Menu */}
          <div className="flex items-center space-x-2">
            {/* Desktop Actions */}
            <div className="hidden lg:flex lg:items-center lg:space-x-2">
              {/* Notifications */}
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <NotificationBell />
              </motion.div>

              {/* Search */}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-all duration-200"
              >
                <Search size={18} />
              </motion.button>

              {/* Divider */}
              <div className="h-6 w-px bg-gray-300 mx-2"></div>
            </div>

            {/* User Profile Dropdown */}
            <div className="relative">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setIsProfileOpen(!isProfileOpen)}
                className="flex items-center space-x-2 p-1.5 rounded-lg hover:bg-gray-50 transition-all duration-200"
              >
                <div className="text-right hidden lg:block">
                  <div className="text-gray-900 text-sm font-medium truncate max-w-24">
                    {session.user?.name}
                  </div>
                  <div className={`text-xs px-1.5 py-0.5 rounded-full ${getRoleBadgeColor(userRole)} font-medium`}>
                    {userRole.charAt(0).toUpperCase() + userRole.slice(1)}
                  </div>
                </div>
                {session.user?.image ? (
                  <img
                    className="h-8 w-8 rounded-full ring-2 ring-gray-200 object-cover"
                    src={session.user.image}
                    alt={session.user.name || 'User'}
                  />
                ) : (
                  <div className="h-8 w-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-medium text-sm">
                    {session.user?.name?.charAt(0) || 'U'}
                  </div>
                )}
                <ChevronDown size={12} className={`transition-transform duration-200 text-gray-400 hidden lg:block ${isProfileOpen ? 'rotate-180' : ''}`} />
              </motion.button>

              {/* Profile Dropdown */}
              <AnimatePresence>
                {isProfileOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: -10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -10, scale: 0.95 }}
                    transition={{ duration: 0.2 }}
                    className="absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-lg border border-gray-200 py-1 z-50"
                  >
                    <div className="px-4 py-3 border-b border-gray-100">
                      <p className="text-sm font-medium text-gray-900 truncate">{session.user?.name}</p>
                      <p className="text-xs text-gray-500 truncate">{session.user?.email}</p>
                    </div>

                    <motion.button
                      whileHover={{ backgroundColor: '#f3f4f6' }}
                      onClick={() => {
                        setIsProfileOpen(false);
                        window.location.href = '/profile';
                      }}
                      className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:text-gray-900 transition-colors"
                    >
                      <User size={16} className="mr-3" />
                      View Profile
                    </motion.button>

                    <motion.button
                      whileHover={{ backgroundColor: '#f3f4f6' }}
                      onClick={() => signOut({ callbackUrl: '/' })}
                      className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:text-gray-900 transition-colors"
                    >
                      <LogOut size={16} className="mr-3" />
                      Sign Out
                    </motion.button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Mobile Menu Button */}
            <div className="lg:hidden flex items-center space-x-2 ml-2">
              {/* Mobile Notifications */}
              <NotificationBell />

              {/* Mobile Menu Button */}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-all duration-200"
              >
                <span className="sr-only">Open main menu</span>
                {!isMenuOpen ? <Menu size={20} /> : <X size={20} />}
              </motion.button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="lg:hidden bg-white border-t border-gray-200"
          >
            <div className="px-4 py-3 space-y-1">
              {navigation.map((item, index) => (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Link
                    href={item.href}
                    className={`${
                      item.current
                        ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    } flex items-center px-3 py-3 rounded-lg text-sm font-medium transition-all duration-200`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <span className="mr-3">{item.icon}</span>
                    <span>{item.name}</span>
                  </Link>
                </motion.div>
              ))}
            </div>

            <div className="border-t border-gray-200 px-4 py-3">
              <div className="flex items-center space-x-3 mb-3">
                {session.user?.image ? (
                  <img
                    className="h-10 w-10 rounded-full ring-2 ring-gray-200 object-cover"
                    src={session.user.image}
                    alt={session.user.name || 'User'}
                  />
                ) : (
                  <div className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-medium">
                    {session.user?.name?.charAt(0) || 'U'}
                  </div>
                )}
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900 truncate">
                    {session.user?.name}
                  </div>
                  <div className="text-xs text-gray-500 truncate">
                    {session.user?.email}
                  </div>
                  <div className={`text-xs px-2 py-1 rounded-full mt-1 inline-block ${getRoleBadgeColor(userRole)}`}>
                    {userRole.charAt(0).toUpperCase() + userRole.slice(1)}
                  </div>
                </div>
              </div>

              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => signOut({ callbackUrl: '/' })}
                className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-red-500 to-pink-500 rounded-lg hover:shadow-lg transition-all duration-200"
              >
                <LogOut size={16} className="mr-2" />
                Sign Out
              </motion.button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  );
}
