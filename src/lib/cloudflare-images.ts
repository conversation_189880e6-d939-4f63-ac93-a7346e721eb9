/**
 * Cloudflare R2 Object Storage integration
 * Handles image upload, compression, and management using R2
 */

import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';

interface R2Config {
  accountId: string;
  accessKeyId: string;
  secretAccessKey: string;
  bucketName: string;
  publicUrl: string;
}

class CloudflareR2Service {
  private s3Client: S3Client | null = null;
  private config: R2Config;
  private isConfigured: boolean = false;

  constructor() {
    this.config = {
      accountId: process.env.CLOUDFLARE_ACCOUNT_ID || '',
      accessKeyId: process.env.R2_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || '',
      bucketName: process.env.R2_BUCKET_NAME || '',
      publicUrl: process.env.R2_PUBLIC_URL || '',
    };

    this.isConfigured = !!(this.config.accountId && this.config.accessKeyId && this.config.secretAccessKey && this.config.bucketName);

    if (!this.isConfigured) {
      console.warn('R2 configuration is missing. Image upload will be disabled. Please set CLOUDFLARE_ACCOUNT_ID, R2_ACCESS_KEY_ID, R2_SECRET_ACCESS_KEY, and R2_BUCKET_NAME environment variables.');
      return;
    }

    // Initialize S3 client for R2 only if configured
    if (this.isConfigured) {
      this.s3Client = new S3Client({
        region: 'auto',
        endpoint: `https://${this.config.accountId}.r2.cloudflarestorage.com`,
        credentials: {
          accessKeyId: this.config.accessKeyId,
          secretAccessKey: this.config.secretAccessKey,
        },
      });
    }
  }

  /**
   * Upload image to R2 Object Storage
   */
  async uploadImage(
    imageBuffer: Buffer,
    filename: string,
    metadata?: Record<string, string>
  ): Promise<string> {
    if (!this.isConfigured || !this.s3Client) {
      throw new Error('R2 is not configured. Please set up your R2 credentials.');
    }

    try {
      const key = `profile-images/${filename}`;

      // Prepare metadata for R2
      const r2Metadata: Record<string, string> = {
        'Content-Type': this.getMimeType(filename),
        'Cache-Control': 'public, max-age=********', // 1 year cache
        ...metadata,
      };

      const command = new PutObjectCommand({
        Bucket: this.config.bucketName,
        Key: key,
        Body: imageBuffer,
        ContentType: this.getMimeType(filename),
        Metadata: r2Metadata,
        ACL: 'public-read', // Make the object publicly readable
      });

      await this.s3Client.send(command);

      // Return the public URL
      return this.getImageUrl(key);
    } catch (error) {
      console.error('Error uploading to R2:', error);
      throw error;
    }
  }

  /**
   * Delete image from R2 Object Storage
   */
  async deleteImage(key: string): Promise<boolean> {
    if (!this.isConfigured || !this.s3Client) {
      console.warn('R2 is not configured. Cannot delete image.');
      return false;
    }

    try {
      const command = new DeleteObjectCommand({
        Bucket: this.config.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
      return true;
    } catch (error) {
      console.error('Error deleting from R2:', error);
      return false;
    }
  }

  /**
   * Get public image URL
   */
  getImageUrl(key: string): string {
    if (this.config.publicUrl) {
      return `${this.config.publicUrl}/${key}`;
    }
    return `https://${this.config.bucketName}.${this.config.accountId}.r2.cloudflarestorage.com/${key}`;
  }

  /**
   * Extract image key from R2 URL
   */
  extractImageKey(url: string): string | null {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      // Remove leading slash
      return pathname.startsWith('/') ? pathname.substring(1) : pathname;
    } catch {
      return null;
    }
  }

  /**
   * Get MIME type from filename
   */
  private getMimeType(filename: string): string {
    const ext = filename.toLowerCase().split('.').pop();
    switch (ext) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      default:
        return 'image/jpeg';
    }
  }
}

export default CloudflareR2Service;
