# ACHY.ME - Employee Bio & Thanks System

A modern, comprehensive employee bio, thanks, and achievement system built with Next.js, MongoDB, and AI-powered features. Features anonymous thanks functionality, role-based access control, and a beautiful modern UI.

## 🚀 Features

### 👤 Employee Profiles
- Detailed employee bios with skills, interests, and professional information
- Profile editing with real-time updates
- Department and role management
- Social links integration (LinkedIn, GitHub)

### 🙏 Thanks System
- Peer-to-peer recognition and appreciation
- Categorized thanks messages (teamwork, leadership, innovation, etc.)
- AI-generated thanks message suggestions
- Public and private thanks options
- Real-time thanks feed

### 🏆 Achievement System
- Automatic achievement generation based on activity
- Multiple achievement types (milestones, recognition, skills, projects, anniversaries)
- Rarity system (common, uncommon, rare, epic, legendary)
- AI-generated achievement titles and descriptions
- Personal achievement dashboard

### 🤖 AI-Powered Features
- Smart thanks message generation using OpenAI
- Automatic achievement creation and descriptions
- Context-aware suggestions based on employee data
- Rate-limited AI usage for cost optimization

### 🔐 Authentication & Security
- Email/password authentication with NextAuth.js
- **Role-based access control (RBAC)** with 4 user levels
- Secure password hashing with bcrypt
- Secure session management with NextAuth.js
- Input validation and sanitization
- Rate limiting and security headers
- CSRF protection

### 👥 Role-Based Access Control
- **Employee**: Basic access to profiles, thanks, and achievements
- **Manager**: Team management and analytics access
- **HR**: Full employee management and department oversight
- **Admin**: Complete system administration and user management

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, MongoDB with Mongoose
- **Authentication**: NextAuth.js with OAuth providers
- **AI**: OpenAI GPT-3.5-turbo
- **Database**: MongoDB Atlas
- **Deployment**: Vercel
- **Styling**: Tailwind CSS with responsive design

## 📋 Prerequisites

- Node.js 18+ and npm
- MongoDB Atlas account
- OpenAI API account (optional, for AI features)
- Vercel account (for deployment)

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/YOUR_USERNAME/achy-me.git
cd achy-me
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Environment Setup

Copy the example environment file:

```bash
cp .env.example .env.local
```

Fill in your environment variables in `.env.local`:

```env
# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/employee-bio-system

# NextAuth.js
NEXTAUTH_SECRET=your-secret-here
NEXTAUTH_URL=http://localhost:3000

# OpenAI (optional)
OPENAI_API_KEY=your-openai-api-key
```

### 4. Run Database Migration (for existing installations)

If you're adding roles to an existing installation:

```bash
npm run migrate:roles
```

This will:
- Add default 'employee' role to existing users
- Optionally create an admin user
- Display role summary

### 5. Run Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 👥 Role Management

### User Roles and Permissions

| Role | Permissions |
|------|-------------|
| **Employee** | View own profile, edit own profile, view public profiles, send thanks, view achievements |
| **Manager** | All employee permissions + view team profiles, edit team profiles, view team analytics, create team achievements |
| **HR** | All employee permissions + view all profiles, edit all profiles, create employees, manage departments, view HR analytics |
| **Admin** | All permissions + manage users, manage roles, view system analytics, manage system settings, delete users |

### Managing User Roles

1. **Admin Panel**: Admins can access `/admin` to manage user roles
2. **API Endpoints**: Use the role management API endpoints
3. **Migration Script**: Use `npm run migrate:roles` for bulk operations

### Creating an Admin User

```bash
# During migration
npm run migrate:roles <EMAIL>

# Or interactively
npm run migrate:roles
```

## 📖 Detailed Setup Guide

### MongoDB Atlas Setup

1. Create a MongoDB Atlas account
2. Create a new cluster
3. Create a database user
4. Whitelist your IP address
5. Get your connection string

### OAuth Provider Setup

#### Google OAuth
1. Go to Google Cloud Console
2. Create/select a project
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs

#### GitHub OAuth
1. Go to GitHub Developer Settings
2. Create a new OAuth App
3. Set authorization callback URL
4. Get Client ID and Secret

### OpenAI API Setup

1. Sign up at OpenAI Platform
2. Generate an API key
3. Add to environment variables

## 🏗️ Project Structure

```
employee-bio-system/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API routes
│   │   │   ├── auth/          # Authentication endpoints
│   │   │   ├── employees/     # Employee management
│   │   │   ├── thanks/        # Thanks system
│   │   │   ├── achievements/  # Achievement system
│   │   │   └── ai/           # AI-powered features
│   │   ├── profile/          # Profile page
│   │   ├── thanks/           # Thanks page
│   │   ├── achievements/     # Achievements page
│   │   └── auth/             # Auth pages
│   ├── components/           # Reusable components
│   ├── lib/                  # Utility libraries
│   │   ├── auth.ts          # NextAuth configuration
│   │   ├── mongodb.ts       # Database connection
│   │   ├── openai.ts        # OpenAI integration
│   │   ├── config.ts        # App configuration
│   │   └── security.ts      # Security utilities
│   ├── models/              # MongoDB schemas
│   │   ├── Employee.ts      # Employee model
│   │   ├── Thanks.ts        # Thanks model
│   │   └── Achievement.ts   # Achievement model
│   └── types/               # TypeScript definitions
├── public/                  # Static assets
├── .env.example            # Environment variables template
├── vercel.json             # Vercel configuration
└── DEPLOYMENT.md           # Deployment guide
```

## 🔧 API Endpoints

### Authentication
- `GET/POST /api/auth/*` - NextAuth.js endpoints

### Employees
- `GET /api/employees` - List all employees
- `POST /api/employees` - Create new employee
- `GET /api/employees/me` - Get current user profile
- `PUT /api/employees/me` - Update current user profile
- `DELETE /api/employees/me` - Delete current user profile

### Thanks
- `GET /api/thanks` - List thanks messages
- `POST /api/thanks` - Send thanks message

### Achievements
- `GET /api/achievements` - List all achievements
- `POST /api/achievements` - Create achievement
- `GET /api/achievements/me` - Get user achievements
- `POST /api/achievements/auto-generate` - Auto-generate achievements

### AI Features
- `POST /api/ai/generate-thanks` - Generate AI thanks message
- `POST /api/ai/generate-achievement` - Generate AI achievement

## 🎨 UI Components

### Navigation
- Responsive navigation bar
- User authentication state
- Mobile-friendly hamburger menu

### Profile Management
- Editable profile forms
- Skill and interest tags
- Social media links
- Profile image display

### Thanks System
- Thanks composition form
- Thanks feed with filtering
- Category-based organization
- AI suggestion integration

### Achievement Display
- Achievement cards with rarity indicators
- Statistics dashboard
- Filter and search functionality
- Badge system with icons and colors

## 🔒 Security Features

### Input Validation
- Server-side validation for all inputs
- XSS prevention through sanitization
- SQL injection protection (NoSQL)
- File upload restrictions

### Authentication Security
- Secure session management
- OAuth provider integration
- CSRF protection
- Rate limiting on sensitive endpoints

### API Security
- Request rate limiting
- Input sanitization
- Error handling without information leakage
- Security headers implementation

## 🚀 Deployment

See [DEPLOYMENT.md](./DEPLOYMENT.md) for detailed deployment instructions.

### Quick Deploy to Vercel

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/achy-me)

**Step-by-Step Deployment:**

1. **Push to GitHub**:
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Deploy to Vercel**:
   - Go to [vercel.com](https://vercel.com) and sign in
   - Click "New Project"
   - Import your GitHub repository
   - Configure environment variables (see below)
   - Click "Deploy"

3. **Required Environment Variables**:
   ```
   NEXTAUTH_URL=https://your-app.vercel.app
   NEXTAUTH_SECRET=your-secret-key-here
   MONGODB_URI=mongodb+srv://username:<EMAIL>/database
   CLOUDFLARE_ACCOUNT_ID=your-cloudflare-account-id
   CLOUDFLARE_ACCESS_KEY_ID=your-r2-access-key
   CLOUDFLARE_SECRET_ACCESS_KEY=your-r2-secret-key
   CLOUDFLARE_BUCKET_NAME=your-r2-bucket-name
   CLOUDFLARE_R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
   OPENAI_API_KEY=sk-your-openai-api-key (optional)
   ```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Test Structure

```
tests/
├── __mocks__/              # Mock files
├── api/                    # API endpoint tests
├── components/             # Component tests
├── lib/                    # Utility function tests
└── setup.ts               # Test setup configuration
```

## 📊 Performance Optimization

### Database Optimization
- Indexed queries for better performance
- Aggregation pipelines for complex queries
- Connection pooling and caching

### Frontend Optimization
- Next.js automatic code splitting
- Image optimization with Next.js Image component
- Lazy loading for better performance
- Responsive design for all devices

### AI Usage Optimization
- Rate limiting to control costs
- Caching for repeated requests
- Fallback messages when AI is unavailable
- Context-aware prompt engineering

## 🔧 Development

### Code Style
- ESLint for code linting
- Prettier for code formatting
- TypeScript for type safety
- Conventional commits for git history

### Development Commands

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Lint code
npm run lint

# Format code
npm run format

# Type check
npm run type-check
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow TypeScript best practices
- Write tests for new features
- Update documentation as needed
- Follow the existing code style
- Ensure all tests pass before submitting

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) for the amazing React framework
- [MongoDB](https://www.mongodb.com/) for the flexible database
- [NextAuth.js](https://next-auth.js.org/) for authentication
- [OpenAI](https://openai.com/) for AI capabilities
- [Tailwind CSS](https://tailwindcss.com/) for styling
- [Vercel](https://vercel.com/) for hosting and deployment

## 📞 Support

For support, email <EMAIL> or join our Slack channel.

## 🗺️ Roadmap

- [ ] Email notifications for thanks and achievements
- [ ] Advanced analytics dashboard
- [ ] Team management features
- [ ] Integration with HR systems
- [ ] Mobile app development
- [ ] Advanced AI features (sentiment analysis, recommendations)
- [ ] Multi-language support
- [ ] Dark mode theme
